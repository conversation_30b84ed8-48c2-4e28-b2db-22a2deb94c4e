#!/bin/bash

# WASM混淆构建脚本
# 使用wasm-pack构建，然后使用wasm-opt进行混淆优化

set -e

echo "🚀 开始构建WASM模块..."

# 第一步：使用wasm-pack构建基础WASM模块
echo "📦 使用wasm-pack构建基础模块..."
wasm-pack build --target web --out-dir pkg-temp

# 第二步：使用wasm-opt进行混淆和优化
echo "🔒 应用混淆和优化..."

# 创建最终输出目录
mkdir -p pkg

# 复制JavaScript绑定文件
cp pkg-temp/*.js pkg/
cp pkg-temp/*.d.ts pkg/
cp pkg-temp/package.json pkg/

# 对WASM文件进行混淆优化
echo "🛡️ 混淆WASM二进制文件..."

# 使用保守的优化策略，避免IR结构问题
echo "🔧 应用混淆优化..."
wasm-opt pkg-temp/*.wasm \
    -O3 \
    --minify-imports-and-exports \
    --remove-unused-module-elements \
    --dce \
    --vacuum \
    -o pkg/$(basename pkg-temp/*.wasm)

# 清理临时目录
rm -rf pkg-temp

# 部署到前端目录
echo "🚀 部署到前端目录..."
mkdir -p ../../public/qudong/wangluoqingqiu
mkdir -p ../../src/qudong/wangluoqingqiu
cp -r pkg/* ../../public/qudong/wangluoqingqiu/
cp -r pkg/* ../../src/qudong/wangluoqingqiu/

echo "✅ 混淆构建完成！"
echo "📁 本地输出目录: pkg/"
echo "📁 前端部署目录: ../../public/qudong/wangluoqingqiu/ 和 ../../src/qudong/wangluoqingqiu/"
echo "📊 文件大小对比:"
echo "原始大小: $(wasm-pack build --target web --out-dir pkg-original >/dev/null 2>&1 && stat -c%s pkg-original/*.wasm && rm -rf pkg-original) bytes"
echo "混淆后大小: $(stat -c%s pkg/*.wasm) bytes"

echo ""
echo "🔍 混淆效果验证:"
echo "- 函数名已被混淆"
echo "- 导入/导出名称已被最小化"
echo "- 未使用的代码已被移除"
echo "- 代码结构已被优化"

echo ""
echo "📝 使用方法:"
echo "import init, { chushihuawangguan, get_qingqiu, post_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';"
