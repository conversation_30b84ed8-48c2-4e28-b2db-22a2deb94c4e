/* tslint:disable */
/* eslint-disable */
export function chushihuawangguan(wangguan_dizhi_canshu: string): void;
export function get_qingqiu(luyou: string, canshu: string | null | undefined, _jiemi_xiangying: boolean, _jiami_qingqiu: boolean, chaoshishijian?: number | null, chongshicishu?: number | null): Promise<any>;
export function post_qingqiu(luyou: string, qingqiuti: string | null | undefined, _jiemi_xiangying: boolean, _jiami_qingqiu: boolean, chaoshishijian?: number | null, chongshicishu?: number | null): Promise<any>;
export function main(): void;

export type InitInput = RequestInfo | URL | Response | BufferSource | WebAssembly.Module;

export interface InitOutput {
  readonly memory: WebAssembly.Memory;
  readonly chushihuawangguan: (a: number, b: number) => void;
  readonly get_qingqiu: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => any;
  readonly post_qingqiu: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => any;
  readonly main: () => void;
  readonly __wbindgen_exn_store: (a: number) => void;
  readonly __externref_table_alloc: () => number;
  readonly __wbindgen_export_2: WebAssembly.Table;
  readonly __wbindgen_export_3: WebAssembly.Table;
  readonly __wbindgen_malloc: (a: number, b: number) => number;
  readonly __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
  readonly _dyn_core__ops__function__FnMut_____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h4da4bba0610f4fe5: (a: number, b: number) => void;
  readonly closure27_externref_shim: (a: number, b: number, c: any) => void;
  readonly closure49_externref_shim: (a: number, b: number, c: any, d: any) => void;
  readonly __wbindgen_start: () => void;
}

export type SyncInitInput = BufferSource | WebAssembly.Module;
/**
* Instantiates the given `module`, which can either be bytes or
* a precompiled `WebAssembly.Module`.
*
* @param {{ module: SyncInitInput }} module - Passing `SyncInitInput` directly is deprecated.
*
* @returns {InitOutput}
*/
export function initSync(module: { module: SyncInitInput } | SyncInitInput): InitOutput;

/**
* If `module_or_path` is {RequestInfo} or {URL}, makes a request and
* for everything else, calls `WebAssembly.instantiate` directly.
*
* @param {{ module_or_path: InitInput | Promise<InitInput> }} module_or_path - Passing `InitInput` directly is deprecated.
*
* @returns {Promise<InitOutput>}
*/
export default function __wbg_init (module_or_path?: { module_or_path: InitInput | Promise<InitInput> } | InitInput | Promise<InitInput>): Promise<InitOutput>;
