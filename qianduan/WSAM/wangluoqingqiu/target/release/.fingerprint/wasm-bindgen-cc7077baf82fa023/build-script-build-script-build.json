{"rustc": 15597765236515928571, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 5408242616063297496, "profile": 11185614270399889401, "path": 10691298388065275129, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-cc7077baf82fa023/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}