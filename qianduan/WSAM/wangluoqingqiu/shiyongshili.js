// WSAM网络请求模块使用示例

import init, { 
    chushihuawangguan, 
    get_qingqiu, 
    post_qingqiu 
} from './pkg/wangluoqingqiu.js';

async function main() {
    // 初始化WebAssembly模块
    await init();
    
    // 初始化网关
    chushihuawangguan('http://127.0.0.1:8098');
    console.log('网关初始化完成');
    
    try {
        // GET请求示例（基本用法）
        const getjieguo1 = await get_qingqiu(
            '/jiekou/nihao',           // 路由
            'param1=value1&param2=value2', // GET参数
            false,                     // 是否解密响应
            false,                     // 是否加密请求
            null,                      // 超时时间（毫秒，null使用默认30秒）
            null                       // 重试次数（null不重试）
        );
        console.log('GET请求结果:', getjieguo1);

        // GET请求示例（带超时和重试）
        const getjieguo2 = await get_qingqiu(
            '/jiekou/nihao',           // 路由
            'param1=value1&param2=value2', // GET参数
            false,                     // 是否解密响应
            false,                     // 是否加密请求
            10000,                     // 超时时间：10秒
            3                          // 重试次数：3次
        );
        console.log('GET请求结果（带重试）:', getjieguo2);

        // POST请求示例（基本用法）
        const postshuju = JSON.stringify({
            name: '测试',
            value: 123
        });

        const postjieguo1 = await post_qingqiu(
            '/jiekou/tijiao',          // 路由
            postshuju,                 // POST请求体
            false,                     // 是否解密响应
            false,                     // 是否加密请求
            null,                      // 超时时间（毫秒，null使用默认30秒）
            null                       // 重试次数（null不重试）
        );
        console.log('POST请求结果:', postjieguo1);

        // POST请求示例（带超时和重试）
        const postjieguo2 = await post_qingqiu(
            '/jiekou/tijiao',          // 路由
            postshuju,                 // POST请求体
            false,                     // 是否解密响应
            false,                     // 是否加密请求
            15000,                     // 超时时间：15秒
            2                          // 重试次数：2次
        );
        console.log('POST请求结果（带重试）:', postjieguo2);

    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 在页面加载完成后执行
if (typeof window !== 'undefined') {
    window.addEventListener('load', main);
} else {
    main();
}
