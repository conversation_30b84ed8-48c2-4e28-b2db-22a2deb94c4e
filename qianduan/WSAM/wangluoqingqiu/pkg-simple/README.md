# WSAM 网络请求模块

这是一个用Rust编写的WebAssembly网络请求库，专门为前端JavaScript调用设计。

## 功能特性

- WebAssembly模块，可在浏览器中运行
- 网关地址内存存储，页面刷新前持久化
- 支持GET和POST请求方法
- 支持URL参数和请求体
- 智能超时控制（默认30秒，可自定义）
- 自动重试机制（支持递增延迟重试）
- 预留加密/解密接口（目前已注释）
- 完整的错误处理

## 项目结构

```
wangluoqingqiu/
├── Cargo.toml                    # 项目配置文件
├── README.md                     # 项目说明文档
├── shiyongshili.js              # JavaScript使用示例
└── src/
    ├── main.rs                   # 主程序入口（WASM不需要）
    ├── lib.rs                    # 库文件
    ├── wangguanquanjuguanli.rs  # 网关全局管理模块
    └── wangluoqingqiu.rs        # 网络请求核心模块
```

## 编译方法

### 安装wasm-pack
```bash
curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
```

### 编译WebAssembly模块
```bash
wasm-pack build --target web
```

## 使用方法

### 1. 初始化网关
```javascript
import init, { chushihuawangguan } from './pkg/wangluoqingqiu.js';

await init();
chushihuawangguan('http://127.0.0.1:8098');
```

### 2. GET请求
```javascript
import { get_qingqiu } from './pkg/wangluoqingqiu.js';

// 基本用法
const jieguo1 = await get_qingqiu(
    '/jiekou/nihao',              // 路由
    'param1=value1&param2=value2', // GET参数
    false,                        // 是否解密响应
    false,                        // 是否加密请求
    null,                         // 超时时间（毫秒，null使用默认30秒）
    null                          // 重试次数（null不重试）
);

// 带超时和重试
const jieguo2 = await get_qingqiu(
    '/jiekou/nihao',              // 路由
    'param1=value1&param2=value2', // GET参数
    false,                        // 是否解密响应
    false,                        // 是否加密请求
    10000,                        // 超时时间：10秒
    3                             // 重试次数：3次
);
```

### 3. POST请求
```javascript
import { post_qingqiu } from './pkg/wangluoqingqiu.js';

const shuju = JSON.stringify({ name: '测试', value: 123 });

// 基本用法
const jieguo1 = await post_qingqiu(
    '/jiekou/tijiao',             // 路由
    shuju,                        // POST请求体
    false,                        // 是否解密响应
    false,                        // 是否加密请求
    null,                         // 超时时间（毫秒，null使用默认30秒）
    null                          // 重试次数（null不重试）
);

// 带超时和重试
const jieguo2 = await post_qingqiu(
    '/jiekou/tijiao',             // 路由
    shuju,                        // POST请求体
    false,                        // 是否解密响应
    false,                        // 是否加密请求
    15000,                        // 超时时间：15秒
    2                             // 重试次数：2次
);
```

## API说明

### chushihuawangguan(wangguan_dizhi: string)
初始化网关地址，存储在内存中直到页面刷新。

### get_qingqiu(luyou, canshu, jiemi_xiangying, jiami_qingqiu, chaoshishijian, chongshicishu)
发送GET请求
- `luyou`: 接口路由，如 '/jiekou/nihao'
- `canshu`: URL参数字符串，如 'param1=value1&param2=value2'
- `jiemi_xiangying`: 是否解密响应（目前未实现）
- `jiami_qingqiu`: 是否加密请求（目前未实现）
- `chaoshishijian`: 超时时间（毫秒），null使用默认30秒
- `chongshicishu`: 重试次数，null不重试

### post_qingqiu(luyou, qingqiuti, jiemi_xiangying, jiami_qingqiu, chaoshishijian, chongshicishu)
发送POST请求
- `luyou`: 接口路由，如 '/jiekou/tijiao'
- `qingqiuti`: 请求体JSON字符串
- `jiemi_xiangying`: 是否解密响应（目前未实现）
- `jiami_qingqiu`: 是否加密请求（目前未实现）
- `chaoshishijian`: 超时时间（毫秒），null使用默认30秒
- `chongshicishu`: 重试次数，null不重试

## 超时和重试机制

### 超时控制
- 默认超时时间：30秒
- 可自定义超时时间（毫秒）
- 超时后自动取消请求

### 重试机制
- 支持自定义重试次数
- 递增延迟重试：第1次重试延迟1秒，第2次延迟2秒，以此类推
- 只有在请求失败或超时时才会重试
- HTTP状态码错误也会触发重试

## 依赖包

- `wasm-bindgen`: WebAssembly绑定
- `web-sys`: Web API绑定
- `js-sys`: JavaScript类型绑定
- `serde`: 序列化/反序列化

## 开发计划

- [ ] 实现加密/解密功能
- [ ] 添加更多HTTP方法支持
- [ ] 请求头自定义
- [x] 超时设置
- [x] 重试机制
