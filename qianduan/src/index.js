import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';

// 防止重复初始化
let wasmInitialized = false;
let initPromise = null;

function App() {
  useEffect(() => {
    let isMounted = true;

    async function ceshiwangluoqingqiu() {
      try {
        // 防止重复初始化
        if (!wasmInitialized && !initPromise) {
          initPromise = init();
          await initPromise;
          wasmInitialized = true;
          console.log('WASM模块初始化成功');
        } else if (initPromise) {
          await initPromise;
        }

        if (!isMounted) return;

        // 初始化网关地址
        chushihuawangguan('http://127.0.0.1:8098');

        if (!isMounted) return;

        // 发送GET请求
        const xiangying = await get_qingqiu(
          '/jiekou/wangzhanjichuxinxi',  // 路由
          null,                         // GET参数
          false,                        // 是否解密响应
          false,                        // 是否加密请求
          5000,                         // 超时时间：5秒
          1                             // 重试次数：1次
        );

        if (isMounted) {
          console.log('网站基础信息响应:', xiangying);
        }

      } catch (cuowu) {
        if (isMounted) {
          console.error('网络请求失败:', cuowu);
        }
      }
    }

    ceshiwangluoqingqiu();

    return () => {
      isMounted = false;
    };
  }, []);

  return (
    <div>
      {/* 空白页面 */}
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      <div style={{ padding: '20px' }}>
        <h1>网络请求测试</h1>
        <p>请查看控制台输出</p>
      </div>
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
