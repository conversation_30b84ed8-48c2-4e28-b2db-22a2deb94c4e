好的，现在我来说一下这个WSAM的实现思路，首先这个wsam不需要有任何的打印，他是一个网络请求的一个wsam库，这个wsam在使用之前，需要初始化，就是需要有一个巧妙的方法，就是我们可以在index，就是前端的任何一个js文件当中去初始化网关，就是调用网络请求必须有一个网关，比如说
http://127.0.o.1:8098
我们必须有一个网关才可以，就是对这个网关进行初始化之后在页面运行初始化之后，我们的wsam就会把这个网关进入一个内存存储，直到我们重新刷新这个网页才会初始化！
初始化完成后，我们就可以随意去调用接口了
有post接口的调用和get接口这两种方法，就是她们支持参数，请求体这些都是支持的
传入的方法也只是需要就是传入参数和路由，比如说我传入/jiekou/nihao
那他就是请求http://127.0.o.1:8098/jiekou/nihao
包括get参数和post参数都是这样的！
然后他还支持是否要解密响应和是否要加密请求，当然这些你先注释一下，目前嘛有任何的加密和解密的功能，但是这个参数还是必须要传的
至于返回，你就直接把内容玩玩整整的返回给这个js调用方就可以了


我需要问一个问题，就是如果我未来加入加解密，前端我现在用的，如果不需要对接加解密，调用方法应该不会有变动吧？
然后是git帮我屏蔽一下这个target

然后是这个wsam有没有什么方法就是有什么库，能够在编译后自动就是进行一些混淆，如果有就帮我添加上，然后再次编译，然后检查是否混淆成功，如果混淆成功了，就把他移动到前端可以调用的地方，public里面也可以